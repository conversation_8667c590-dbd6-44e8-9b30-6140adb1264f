<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>pkg_gazebo</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">skysky</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <!-- Dependencies for robot description -->
  <depend>robot_state_publisher</depend>
  <depend>joint_state_publisher</depend>
  <depend>xacro</depend>

  <!-- Dependencies for Gazebo Harmonic simulation -->
  <depend>ros_gz_sim</depend>
  <depend>ros_gz_bridge</depend>
  <depend>ros_gz_interfaces</depend>

  <!-- Dependencies for ros2_control -->
  <depend>ros2_control</depend>
  <depend>ros2_controllers</depend>
  <depend>gz_ros2_control</depend>
  <depend>controller_manager</depend>

  <!-- Dependencies for visualization -->
  <depend>rviz2</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
