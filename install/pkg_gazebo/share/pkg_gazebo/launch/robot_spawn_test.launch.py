#!/usr/bin/env python3

import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import DeclareLaunchArgument, TimerAction, ExecuteProcess
from launch.substitutions import LaunchConfiguration, FindExecutable
from launch.conditions import IfCondition, UnlessCondition


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # Launch参数
    headless_arg = DeclareLaunchArgument(
        'headless',
        default_value='false',
        description='Run Gazebo in headless mode (no GUI)'
    )
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    world_file = os.path.join(pkg_gazebo, 'worlds', 'movement_test.world')
    
    # 机器人描述参数（稳健调用xacro）
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 启动Gazebo Harmonic
    headless = LaunchConfiguration('headless')

    # 将包的share路径加入资源搜索路径，确保file://$(find pkg_gazebo) 可被解析
    env_overrides = os.environ.copy()
    pkg_share = pkg_gazebo
    env_var = 'GZ_SIM_RESOURCE_PATH'
    # 兼容旧变量名
    if 'GZ_RESOURCE_PATH' in env_overrides:
      env_overrides['GZ_RESOURCE_PATH'] = env_overrides['GZ_RESOURCE_PATH'] + os.pathsep + pkg_share
    else:
      env_overrides['GZ_RESOURCE_PATH'] = pkg_share
    if env_var in env_overrides:
      env_overrides[env_var] = env_overrides[env_var] + os.pathsep + pkg_share
    else:
      env_overrides[env_var] = pkg_share

    # Gazebo Harmonic with GUI
    gazebo_gui = ExecuteProcess(
        cmd=['gz', 'sim', world_file, '-v', '4'],
        output='screen',
        condition=UnlessCondition(headless),
        additional_env=env_overrides
    )

    # Gazebo Harmonic headless
    gazebo_server = ExecuteProcess(
        cmd=['gz', 'sim', world_file, '-v', '4', '-s'],
        output='screen',
        condition=IfCondition(headless),
        additional_env=env_overrides
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 延迟生成机器人（使用Gazebo Harmonic的create）
    # 明确指定世界名，确保机器人创建到正确的世界
    spawn_entity = TimerAction(
        period=10.0,  # 给Gazebo更多时间启动
        actions=[
            launch_ros.actions.Node(
                package='ros_gz_sim',
                executable='create',
                arguments=[
                    '-world', 'movement_test',
                    '-topic', 'robot_description',
                    '-name', 'urbubing',
                    '-x', '0.0',
                    '-y', '0.0',
                    '-z', '0.5'
                ],
                output='screen'
            )
        ]
    )

    return launch.LaunchDescription([
        headless_arg,
        gazebo_gui,
        gazebo_server,
        robot_state_publisher,
        spawn_entity
    ])
