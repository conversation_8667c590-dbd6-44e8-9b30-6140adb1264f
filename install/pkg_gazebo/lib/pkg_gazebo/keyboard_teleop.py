#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
import sys
import select
import termios
import tty

class KeyboardTeleop(Node):
    def __init__(self):
        super().__init__('keyboard_teleop')
        
        # 发布cmd_vel话题
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        
        # 运动参数
        self.linear_speed = 1.0    # 线速度 (m/s)
        self.angular_speed = 1.0   # 角速度 (rad/s)
        self.speed_step = 0.1      # 速度调节步长
        
        # 当前速度
        self.current_linear_x = 0.0
        self.current_linear_y = 0.0
        self.current_angular_z = 0.0
        
        # 保存终端设置
        self.settings = termios.tcgetattr(sys.stdin)
        
        self.print_instructions()
        
    def print_instructions(self):
        """打印控制说明"""
        print("\n" + "="*60)
        print("🚗 麦克纳姆轮机器人键盘控制")
        print("="*60)
        print("移动控制:")
        print("   w/s : 前进/后退")
        print("   a/d : 左移/右移") 
        print("   q/e : 左转/右转")
        print("")
        print("组合移动 (斜向移动):")
        print("   r : 右前斜移    t : 左前斜移")
        print("   f : 右后斜移    g : 左后斜移")
        print("")
        print("速度控制:")
        print("   u/j : 增加/减少线速度")
        print("   i/k : 增加/减少角速度")
        print("   空格: 紧急停止")
        print("")
        print("其他:")
        print("   h : 显示帮助")
        print("   Ctrl+C : 退出")
        print("="*60)
        print(f"当前速度设置: 线速度={self.linear_speed:.1f}m/s, 角速度={self.angular_speed:.1f}rad/s")
        print("按任意键开始控制...")
        
    def get_key(self):
        """获取键盘输入"""
        try:
            tty.setraw(sys.stdin.fileno())
            # 使用非阻塞读取
            if select.select([sys.stdin], [], [], 0.1)[0]:
                key = sys.stdin.read(1)
            else:
                key = ''
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
            return key
        except Exception as e:
            self.get_logger().error(f'键盘读取错误: {e}')
            return ''
        
    def publish_twist(self, linear_x=0.0, linear_y=0.0, angular_z=0.0):
        """发布Twist消息"""
        twist = Twist()
        twist.linear.x = linear_x
        twist.linear.y = linear_y
        twist.angular.z = angular_z
        self.cmd_vel_pub.publish(twist)
        
        # 更新当前速度
        self.current_linear_x = linear_x
        self.current_linear_y = linear_y
        self.current_angular_z = angular_z
        
    def print_status(self):
        """打印当前状态"""
        print(f"\r当前命令: vx={self.current_linear_x:.2f}, vy={self.current_linear_y:.2f}, wz={self.current_angular_z:.2f} | "
              f"速度设置: 线速度={self.linear_speed:.1f}, 角速度={self.angular_speed:.1f}", end='', flush=True)
        
    def run(self):
        """主控制循环"""
        try:
            while rclpy.ok():
                key = self.get_key()
                
                if key == '\x03':  # Ctrl+C
                    break
                    
                # 基本移动控制
                elif key == 'w':  # 前进
                    self.publish_twist(linear_x=self.linear_speed)
                    print(f"\n前进 (速度: {self.linear_speed:.1f}m/s)")
                    
                elif key == 's':  # 后退
                    self.publish_twist(linear_x=-self.linear_speed)
                    print(f"\n后退 (速度: {self.linear_speed:.1f}m/s)")
                    
                elif key == 'a':  # 左移
                    self.publish_twist(linear_y=self.linear_speed)
                    print(f"\n左移 (速度: {self.linear_speed:.1f}m/s)")
                    
                elif key == 'd':  # 右移
                    self.publish_twist(linear_y=-self.linear_speed)
                    print(f"\n右移 (速度: {self.linear_speed:.1f}m/s)")
                    
                elif key == 'q':  # 左转
                    self.publish_twist(angular_z=self.angular_speed)
                    print(f"\n左转 (角速度: {self.angular_speed:.1f}rad/s)")
                    
                elif key == 'e':  # 右转
                    self.publish_twist(angular_z=-self.angular_speed)
                    print(f"\n右转 (角速度: {self.angular_speed:.1f}rad/s)")
                    
                # 斜向移动
                elif key == 'r':  # 右前斜移
                    self.publish_twist(linear_x=self.linear_speed, linear_y=-self.linear_speed)
                    print(f"\n右前斜移")
                    
                elif key == 't':  # 左前斜移
                    self.publish_twist(linear_x=self.linear_speed, linear_y=self.linear_speed)
                    print(f"\n左前斜移")
                    
                elif key == 'f':  # 右后斜移
                    self.publish_twist(linear_x=-self.linear_speed, linear_y=-self.linear_speed)
                    print(f"\n右后斜移")
                    
                elif key == 'g':  # 左后斜移
                    self.publish_twist(linear_x=-self.linear_speed, linear_y=self.linear_speed)
                    print(f"\n左后斜移")
                    
                # 速度调节
                elif key == 'u':  # 增加线速度
                    self.linear_speed = min(3.0, self.linear_speed + self.speed_step)
                    print(f"\n线速度增加到: {self.linear_speed:.1f}m/s")
                    
                elif key == 'j':  # 减少线速度
                    self.linear_speed = max(0.1, self.linear_speed - self.speed_step)
                    print(f"\n线速度减少到: {self.linear_speed:.1f}m/s")
                    
                elif key == 'i':  # 增加角速度
                    self.angular_speed = min(3.0, self.angular_speed + self.speed_step)
                    print(f"\n角速度增加到: {self.angular_speed:.1f}rad/s")
                    
                elif key == 'k':  # 减少角速度
                    self.angular_speed = max(0.1, self.angular_speed - self.speed_step)
                    print(f"\n角速度减少到: {self.angular_speed:.1f}rad/s")
                    
                # 停止
                elif key == ' ':  # 空格键停止
                    self.publish_twist()
                    print(f"\n🛑 紧急停止!")
                    
                # 帮助
                elif key == 'h':
                    self.print_instructions()
                    
                else:
                    # 未知按键，停止运动
                    self.publish_twist()
                    
                self.print_status()
                
        except Exception as e:
            self.get_logger().error(f'Error: {e}')
        finally:
            # 确保停止机器人
            self.publish_twist()
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)

def main(args=None):
    rclpy.init(args=args)
    
    teleop = KeyboardTeleop()
    
    try:
        teleop.run()
    except KeyboardInterrupt:
        pass
    finally:
        teleop.destroy_node()
        rclpy.shutdown()
        print("\n键盘控制已退出")

if __name__ == '__main__':
    main()
