#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import Float64MultiArray
import math

class MecanumController(Node):
    def __init__(self):
        super().__init__('mecanum_controller')
        
        # 订阅cmd_vel话题
        self.cmd_vel_sub = self.create_subscription(
            Twist,
            '/cmd_vel',
            self.cmd_vel_callback,
            10
        )
        
        # 发布轮子速度命令
        self.wheel_cmd_pub = self.create_publisher(
            Float64MultiArray,
            '/wheel_controller/commands',
            10
        )
        
        # 机器人参数 (单位: 米)
        self.wheel_radius = 0.05  # 轮子半径
        self.wheel_base_width = 0.35  # 轮距 (左右轮子间距)
        self.wheel_base_length = 0.38  # 轴距 (前后轮子间距)
        
        # 计算机器人几何参数
        self.lx = self.wheel_base_length / 2.0  # 前轮到中心距离
        self.ly = self.wheel_base_width / 2.0   # 左轮到中心距离
        
        self.get_logger().info('Mecanum Controller initialized')
        self.get_logger().info(f'Wheel radius: {self.wheel_radius}m')
        self.get_logger().info(f'Wheel base: {self.wheel_base_width}m x {self.wheel_base_length}m')
        
    def cmd_vel_callback(self, msg):
        """
        将cmd_vel转换为四个麦克纳姆轮的速度命令
        
        麦克纳姆轮运动学方程:
        vx = (v_lf + v_rf + v_rb + v_lb) * R / 4
        vy = (-v_lf + v_rf - v_rb + v_lb) * R / 4  
        wz = (-v_lf - v_rf + v_rb + v_lb) * R / (4 * (lx + ly))
        
        反解得到轮子速度:
        v_lf = (vx - vy - wz * (lx + ly)) / R
        v_rf = (vx + vy - wz * (lx + ly)) / R
        v_rb = (vx - vy + wz * (lx + ly)) / R
        v_lb = (vx + vy + wz * (lx + ly)) / R
        """
        
        # 提取线速度和角速度
        vx = msg.linear.x   # 前进/后退速度 (m/s)
        vy = msg.linear.y   # 左右平移速度 (m/s)
        wz = msg.angular.z  # 旋转角速度 (rad/s)
        
        # 计算每个轮子的线速度 (m/s)
        v_lf = (vx - vy - wz * (self.lx + self.ly))  # 左前轮
        v_rf = (vx + vy - wz * (self.lx + self.ly))  # 右前轮
        v_rb = (vx - vy + wz * (self.lx + self.ly))  # 右后轮
        v_lb = (vx + vy + wz * (self.lx + self.ly))  # 左后轮
        
        # 转换为轮子角速度 (rad/s)
        omega_lf = v_lf / self.wheel_radius
        omega_rf = v_rf / self.wheel_radius
        omega_rb = v_rb / self.wheel_radius
        omega_lb = v_lb / self.wheel_radius
        
        # 创建轮子速度命令消息
        wheel_cmd = Float64MultiArray()
        wheel_cmd.data = [omega_lf, omega_rf, omega_rb, omega_lb]
        
        # 发布轮子速度命令
        self.wheel_cmd_pub.publish(wheel_cmd)
        
        # 调试信息
        if abs(vx) > 0.01 or abs(vy) > 0.01 or abs(wz) > 0.01:
            self.get_logger().info(
                f'Cmd: vx={vx:.2f}, vy={vy:.2f}, wz={wz:.2f} | '
                f'Wheels: lf={omega_lf:.2f}, rf={omega_rf:.2f}, '
                f'rb={omega_rb:.2f}, lb={omega_lb:.2f} rad/s'
            )

def main(args=None):
    rclpy.init(args=args)
    
    controller = MecanumController()
    
    try:
        rclpy.spin(controller)
    except KeyboardInterrupt:
        pass
    finally:
        controller.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
