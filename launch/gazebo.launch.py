import os
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import Command, FindExecutable
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # 包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    
    # 控制器配置文件路径
    controllers_file = os.path.join(pkg_gazebo, 'config', 'controllers.yaml')
    
    # robot_description 参数 (用 xacro 正确展开)
    robot_description = {
        'robot_description': Command([FindExecutable(name='xacro'), ' ', urdf_file])
    }
    
    # 启动 Gazebo Harmonic
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('ros_gz_sim'), 'launch', 'gz_sim.launch.py')
        ]),
        launch_arguments={
            'gz_args': [os.path.join(pkg_gazebo, 'worlds', 'empty.world'), ' -v 4'],
            'on_exit_shutdown': 'true'
        }.items()
    )
    
    # 机器人状态发布器
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[robot_description]
    )
    
    # 在 Gazebo 中生成机器人
    spawn_entity = Node(
        package='ros_gz_sim',
        executable='create',
        arguments=['-topic', 'robot_description', '-name', 'urbubing', '-z', '0.2'],
        output='screen'
    )
    
    # 控制器管理器
    controller_manager = Node(
        package='controller_manager',
        executable='ros2_control_node',
        parameters=[robot_description, controllers_file],
        output='screen'
    )
    
    # 启动控制器
    joint_state_broadcaster_spawner = Node(
        package='controller_manager',
        executable='spawner',
        arguments=['joint_state_broadcaster'],
        output='screen'
    )
    
    wheel_controller_spawner = Node(
        package='controller_manager',
        executable='spawner',
        arguments=['wheel_controller'],
        output='screen'
    )
    
    gimbal_controller_spawner = Node(
        package='controller_manager',
        executable='spawner',
        arguments=['gimbal_controller'],
        output='screen'
    )
    
    shooter_controller_spawner = Node(
        package='controller_manager',
        executable='spawner',
        arguments=['shooter_controller'],
        output='screen'
    )
    
    return LaunchDescription([
        gazebo,
        robot_state_publisher,
        spawn_entity,
        controller_manager,
        joint_state_broadcaster_spawner,
        wheel_controller_spawner,
        gimbal_controller_spawner,
        shooter_controller_spawner
    ])
