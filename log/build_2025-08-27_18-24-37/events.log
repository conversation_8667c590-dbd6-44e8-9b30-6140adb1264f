[0.000000] (-) TimerEvent: {}
[0.000527] (pkg_gazebo) JobQueued: {'identifier': 'pkg_gazebo', 'dependencies': OrderedDict()}
[0.000581] (pkg_gazebo) JobStarted: {'identifier': 'pkg_gazebo'}
[0.012080] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'cmake'}
[0.013604] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'build'}
[0.013666] (pkg_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'en_US.UTF-8', 'SYSTEMD_EXEC_PID': '6041', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/5995,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/5995', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/23ea472a_9dd7_4917_9b37_2f2e8113f021', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.KZ4TB3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.1146', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.077799] (pkg_gazebo) CommandEnded: {'returncode': 0}
[0.078617] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'install'}
[0.090280] (pkg_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo'], 'cwd': '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'en_US.UTF-8', 'SYSTEMD_EXEC_PID': '6041', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/5995,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/5995', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/23ea472a_9dd7_4917_9b37_2f2e8113f021', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.KZ4TB3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.1146', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.099832] (-) TimerEvent: {}
[0.100042] (pkg_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.100235] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch\n'}
[0.100338] (pkg_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo.launch.py\n'}
[0.100476] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/robot_spawn_test.launch.py\n'}
[0.100571] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py\n'}
[0.100812] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo_visual_test.launch.py\n'}
[0.100903] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/movement_test.launch.py\n'}
[0.100985] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf\n'}
[0.101062] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf.xacro\n'}
[0.101148] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes\n'}
[0.101227] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL\n'}
[0.101317] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL\n'}
[0.101398] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL\n'}
[0.101475] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL\n'}
[0.101550] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL\n'}
[0.101792] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL\n'}
[0.101876] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL\n'}
[0.101948] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL\n'}
[0.102028] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL\n'}
[0.102106] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL\n'}
[0.102184] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL\n'}
[0.102264] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config\n'}
[0.102346] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml\n'}
[0.102421] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/controllers.yaml\n'}
[0.102494] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz\n'}
[0.102569] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py\n'}
[0.102643] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//worlds\n'}
[0.102720] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//worlds/movement_test.world\n'}
[0.102920] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//worlds/physics_test.world\n'}
[0.102990] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//worlds/empty.world\n'}
[0.103056] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/lib/pkg_gazebo/mecanum_controller.py\n'}
[0.103120] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/lib/pkg_gazebo/keyboard_teleop.py\n'}
[0.103186] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/lib/pkg_gazebo/gazebo_mecanum_controller.py\n'}
[0.103286] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo\n'}
[0.103380] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo\n'}
[0.103470] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh\n'}
[0.103554] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv\n'}
[0.103648] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh\n'}
[0.103756] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv\n'}
[0.104221] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash\n'}
[0.104858] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh\n'}
[0.104955] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh\n'}
[0.105442] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv\n'}
[0.105544] (pkg_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.dsv\n'}
[0.105628] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo\n'}
[0.105706] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake\n'}
[0.105841] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake\n'}
[0.105918] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.xml\n'}
[0.105999] (pkg_gazebo) CommandEnded: {'returncode': 0}
[0.120645] (pkg_gazebo) JobEnded: {'identifier': 'pkg_gazebo', 'rc': 0}
[0.121256] (-) EventReactorShutdown: {}
