<?xml version="1.0" encoding="utf-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="urbubing">

  <!-- ==================== PARAMETERS ==================== -->
  
  <!-- Robot basic parameters -->
  <xacro:property name="robot_name" value="urbubing" />
  <xacro:property name="package_name" value="pkg_gazebo" />
  
  <!-- Wheel parameters -->
  <xacro:property name="wheel_mass" value="0.255" />
  <xacro:property name="wheel_radius" value="0.05" />
  <xacro:property name="wheel_width" value="0.03" />
  <xacro:property name="wheel_effort_limit" value="100" />
  <xacro:property name="wheel_velocity_limit" value="100" />

  <!-- Physical properties -->
  <xacro:property name="wheel_friction" value="1.0" />
  <xacro:property name="wheel_damping" value="0.1" />
  <xacro:property name="joint_damping" value="0.1" />
  <xacro:property name="joint_friction" value="0.1" />
  
  <!-- Base parameters -->
  <xacro:property name="base_mass" value="8.868" />
  <xacro:property name="base_length" value="0.4" />
  <xacro:property name="base_width" value="0.35" />
  <xacro:property name="base_height" value="0.15" />
  
  <!-- Gimbal parameters -->
  <xacro:property name="yaw_mass" value="1.346" />
  <xacro:property name="pitch_mass" value="0.232" />
  <xacro:property name="gimbal_effort_limit" value="100" />
  <xacro:property name="gimbal_velocity_limit" value="100" />
  
  <!-- Pitch limits (in radians) -->
  <xacro:property name="pitch_lower_limit" value="-0.7854" />  <!-- -45 degrees -->
  <xacro:property name="pitch_upper_limit" value="0.5236" />   <!-- 30 degrees -->
  
  <!-- Shooter parameters -->
  <xacro:property name="shooter_mass" value="0.192" />
  
  <!-- ==================== MATERIALS ==================== -->
  
  <material name="base_color">
    <color rgba="0.753 0.753 0.753 1" />
  </material>
  
  <material name="wheel_color">
    <color rgba="1 1 1 1" />
  </material>
  
  <material name="gimbal_color">
    <color rgba="1 1 1 1" />
  </material>
  
  <material name="aid_color">
    <color rgba="0.776 0.757 0.737 1" />
  </material>
  
  <!-- ==================== MACROS ==================== -->
  
  <!-- Wheel macro -->
  <xacro:macro name="wheel" params="prefix parent_link xyz rpy">
    <link name="wheel_${prefix}_Link">
      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <mass value="${wheel_mass}" />
        <inertia 
          ixx="${wheel_mass * (3 * wheel_radius * wheel_radius + wheel_width * wheel_width) / 12}"
          ixy="0" ixz="0"
          iyy="${wheel_mass * (3 * wheel_radius * wheel_radius + wheel_width * wheel_width) / 12}"
          iyz="0"
          izz="${wheel_mass * wheel_radius * wheel_radius / 2}" />
      </inertial>
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://pkg_gazebo/meshes/wheel_${prefix}_Link.STL" />
        </geometry>
        <material name="wheel_color" />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}" />
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>${wheel_friction}</mu>
              <mu2>${wheel_friction}</mu2>
            </ode>
          </friction>
        </surface>
      </collision>
    </link>

    <joint name="wheel_${prefix}_Joint" type="continuous">
      <origin xyz="${xyz}" rpy="${rpy}" />
      <parent link="${parent_link}" />
      <child link="wheel_${prefix}_Link" />
      <axis xyz="0 0 -1" />
      <limit effort="${wheel_effort_limit}" velocity="${wheel_velocity_limit}" />
      <dynamics damping="${joint_damping}" friction="${joint_friction}" />
    </joint>
  </xacro:macro>
  
  <!-- ==================== BASE LINK ==================== -->
  
  <link name="base_link">
    <inertial>
      <origin xyz="0.0433 0.0105 -0.0235" rpy="0 0 0" />
      <mass value="${base_mass}" />
      <inertia
        ixx="0.0142" ixy="0.0006" ixz="-0.0001"
        iyy="0.0202" iyz="0.0000"
        izz="0.0310" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/base_link.STL" />
      </geometry>
      <material name="base_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}" />
      </geometry>
    </collision>
  </link>
  
  <!-- ==================== WHEELS ==================== -->
  
  <!-- Left Front Wheel -->
  <xacro:wheel prefix="lf" parent_link="base_link" 
               xyz="-0.19103 -0.17579 -0.073501" 
               rpy="1.5708 0 -3.1416" />
  
  <!-- Right Front Wheel -->
  <xacro:wheel prefix="rf" parent_link="base_link" 
               xyz="-0.19103 0.17321 -0.073501" 
               rpy="-1.5708 0 3.1416" />
  
  <!-- Right Back Wheel -->
  <xacro:wheel prefix="rb" parent_link="base_link" 
               xyz="0.18833 0.17052 -0.073501" 
               rpy="1.5708 0 -3.1416" />
  
  <!-- Left Back Wheel -->
  <xacro:wheel prefix="lb" parent_link="base_link" 
               xyz="0.18788 -0.17579 -0.054692" 
               rpy="1.5708 0 3.1416" />
  
  <!-- ==================== GIMBAL SYSTEM ==================== -->
  
  <!-- Yaw Link -->
  <link name="yaw_Link">
    <inertial>
      <origin xyz="-0.0011 0.0038 -0.0922" rpy="0 0 0" />
      <mass value="${yaw_mass}" />
      <inertia
        ixx="0.0012" ixy="0" ixz="0"
        iyy="0.0013" iyz="0"
        izz="0.0015" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/yaw_Link.STL" />
      </geometry>
      <material name="gimbal_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/yaw_Link.STL" />
      </geometry>
    </collision>
  </link>
  
  <joint name="yaw_Joint" type="continuous">
    <origin xyz="-0.0014015 -0.0018662 -0.050192" rpy="3.1416 0 0" />
    <parent link="base_link" />
    <child link="yaw_Link" />
    <axis xyz="0 0 -1" />
    <limit effort="${gimbal_effort_limit}" velocity="${gimbal_velocity_limit}" />
    <dynamics damping="${joint_damping}" friction="${joint_friction}" />
  </joint>
  
  <!-- Pitch Link -->
  <link name="pitch_Link">
    <inertial>
      <origin xyz="-0.0007 0.0001 0.0369" rpy="0 0 0" />
      <mass value="${pitch_mass}" />
      <inertia
        ixx="0.0001" ixy="0" ixz="0"
        iyy="0.0001" iyz="0"
        izz="0.0001" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_Link.STL" />
      </geometry>
      <material name="gimbal_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_Link.STL" />
      </geometry>
    </collision>
  </link>
  
  <joint name="pitch_Joint" type="revolute">
    <origin xyz="0.00047466 -0.059676 -0.1331" rpy="-1.5708 0 0.0071369" />
    <parent link="yaw_Link" />
    <child link="pitch_Link" />
    <axis xyz="0 0 -1" />
    <limit lower="${pitch_lower_limit}" upper="${pitch_upper_limit}"
           effort="${gimbal_effort_limit}" velocity="${gimbal_velocity_limit}" />
    <dynamics damping="${joint_damping}" friction="${joint_friction}" />
  </joint>

  <!-- ==================== PITCH AID SYSTEM ==================== -->

  <!-- Pitch Aid Link 1 -->
  <link name="pitch_aid_Link1">
    <inertial>
      <origin xyz="0.0000433 0.0377 -0.0061" rpy="0 0 0" />
      <mass value="0.0284" />
      <inertia
        ixx="0.0000261" ixy="0" ixz="0"
        iyy="0.0000010" iyz="0"
        izz="0.0000263" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_aid_Link1.STL" />
      </geometry>
      <material name="aid_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_aid_Link1.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="pitch_aid_Joint1" type="revolute">
    <origin xyz="-0.04 0 0.0015" rpy="0 0.0071369 0" />
    <parent link="pitch_Link" />
    <child link="pitch_aid_Link1" />
    <axis xyz="0.0071368 0 -0.99997" />
    <limit lower="-1.5708" upper="1.5078"
           effort="${gimbal_effort_limit}" velocity="${gimbal_velocity_limit}" />
  </joint>

  <!-- Pitch Aid Link 2 -->
  <link name="pitch_aid_Link2">
    <inertial>
      <origin xyz="0.0232 -0.0027 0.0006" rpy="0 0 0" />
      <mass value="0.0526" />
      <inertia
        ixx="0.0000053" ixy="0.0000006" ixz="0"
        iyy="0.0000118" iyz="0"
        izz="0.0000159" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_aid_Link2.STL" />
      </geometry>
      <material name="aid_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_aid_Link2.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="pitch_aid_Joint2" type="revolute">
    <origin xyz="0.00010777 0.121 -0.0151" rpy="0 -0.0071369 0" />
    <parent link="pitch_aid_Link1" />
    <child link="pitch_aid_Link2" />
    <axis xyz="0 0 -1" />
    <limit lower="-0.5236" upper="0.7854"
           effort="${gimbal_effort_limit}" velocity="${gimbal_velocity_limit}" />
  </joint>

  <!-- Pitch Aid Link 3 -->
  <link name="pitch_aid_Link3">
    <inertial>
      <origin xyz="0.0100 0.0101 0.0700" rpy="0 0 0" />
      <mass value="1.580" />
      <inertia
        ixx="0.0008" ixy="0.0000196" ixz="0"
        iyy="0.0016" iyz="0"
        izz="0.0011" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_aid_Link3.STL" />
      </geometry>
      <material name="aid_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/pitch_aid_Link3.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="pitch_aid_Joint3" type="revolute">
    <origin xyz="0.04 0 0" rpy="0 0 0" />
    <parent link="pitch_aid_Link2" />
    <child link="pitch_aid_Link3" />
    <axis xyz="0 0 -1" />
    <limit lower="-0.5236" upper="0.7854"
           effort="${gimbal_effort_limit}" velocity="${gimbal_velocity_limit}" />
  </joint>

  <!-- ==================== SHOOTER SYSTEM ==================== -->

  <!-- Shooter/Dials Link -->
  <link name="dials_Link">
    <inertial>
      <origin xyz="-0.0004 -0.0006 0.0207" rpy="0 0 0" />
      <mass value="${shooter_mass}" />
      <inertia
        ixx="0.0001" ixy="0" ixz="0"
        iyy="0.0001" iyz="0"
        izz="0.0003" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/dials_Link.STL" />
      </geometry>
      <material name="wheel_color" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://pkg_gazebo/meshes/dials_Link.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="dials_Joint" type="continuous">
    <origin xyz="-0.064978 -0.0010679 -0.2856" rpy="0 0 0" />
    <parent link="yaw_Link" />
    <child link="dials_Link" />
    <axis xyz="-0.10617 0.0076706 -0.99432" />
    <limit effort="${gimbal_effort_limit}" velocity="${gimbal_velocity_limit}" />
    <dynamics damping="${joint_damping}" friction="${joint_friction}" />
  </joint>

  <!-- ==================== ROS2 CONTROL ==================== -->

  <ros2_control name="urbubing_system" type="system">
    <hardware>
      <plugin>gz_ros2_control/GazeboSimSystem</plugin>
    </hardware>

    <!-- Wheel joints -->
    <joint name="wheel_lf_Joint">
      <command_interface name="velocity">
        <param name="min">-${wheel_velocity_limit}</param>
        <param name="max">${wheel_velocity_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>

    <joint name="wheel_rf_Joint">
      <command_interface name="velocity">
        <param name="min">-${wheel_velocity_limit}</param>
        <param name="max">${wheel_velocity_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>

    <joint name="wheel_rb_Joint">
      <command_interface name="velocity">
        <param name="min">-${wheel_velocity_limit}</param>
        <param name="max">${wheel_velocity_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>

    <joint name="wheel_lb_Joint">
      <command_interface name="velocity">
        <param name="min">-${wheel_velocity_limit}</param>
        <param name="max">${wheel_velocity_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>

    <!-- Gimbal joints -->
    <joint name="yaw_Joint">
      <command_interface name="velocity">
        <param name="min">-${gimbal_velocity_limit}</param>
        <param name="max">${gimbal_velocity_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>

    <joint name="pitch_Joint">
      <command_interface name="position">
        <param name="min">${pitch_lower_limit}</param>
        <param name="max">${pitch_upper_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>

    <!-- Shooter joint -->
    <joint name="dials_Joint">
      <command_interface name="velocity">
        <param name="min">-${gimbal_velocity_limit}</param>
        <param name="max">${gimbal_velocity_limit}</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
  </ros2_control>

  <!-- ==================== GAZEBO PLUGINS ==================== -->

  <gazebo>
    <plugin filename="gz_ros2_control-system" name="gz_ros2_control::GazeboSimROS2ControlPlugin">
      <parameters>$(find pkg_gazebo)/config/controllers.yaml</parameters>
      <ros>
        <namespace></namespace>
        <remapping>~/robot_description:=robot_description</remapping>
      </ros>
    </plugin>
  </gazebo>

</robot>
